import {useState, useEffect, useRef} from 'react';
import styled from '@emotion/styled';
import {Space} from 'antd';
import {StageStep} from '@/types/staff/stage';
import {
    renderStepContent,
    renderExpandIcon,
    renderStepIcon,
    renderInnerExpandIcon,
} from './PlaygroundStageStepsRenderers';

interface ItemsProps {
    steps: StageStep[];
    isOuterLayer?: boolean;
}

const Container = styled.div<{isOuterLayer?: boolean}>`
    display: flex;
    flex-direction: column;
    ${props => (!props.isOuterLayer ? 'gap: 8px;' : '')}
`;

const StepContainer = styled.div<{isOuterLayer?: boolean}>`
    ${props => (props.isOuterLayer
        ? 'margin-bottom: 8px;'
        : 'background-color: #F5F7FA; border-radius: 8px; padding: 3px 16px;')}
    &:last-child { margin-bottom: 0; }
`;

const StepHeader = styled.div<{isOuterLayer?: boolean}>`
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 3px 0;
    border-radius: 4px;
    transition: background-color 0.2s;
    ${props => (props.isOuterLayer ? 'justify-content: flex-start;' : 'justify-content: space-between;')}
`;

const StepTitleWrapper = styled.div<{isOuterLayer?: boolean}>`
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 0;
    ${props => (!props.isOuterLayer ? 'margin-right: 24px;' : '')}
`;

const StepTitle = styled.div<{hasTooltip?: boolean}>`
    font: 500 14px/22px PingFang SC;
    color: #110f16;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
`;
const StatusBadge = styled.div<{isOuterLayer?: boolean}>`
    font: 400 14px/22px PingFang SC; color: #EB4561;
    min-width: 28px;
    ${props => (props.isOuterLayer
        ? 'background: #FFDCE2; border-radius: 44px; padding: 0 6px; min-width: 32px; text-align: center;'
        : '')}
`;

function StepItem({step, isOuterLayer}: {step: StageStep, isOuterLayer: boolean}) {
    const isCompleted = step.status === 'success' || step.status === 'failed';
    const [expanded, setExpanded] = useState(true);
    const [isTextTruncated, setIsTextTruncated] = useState(false);
    const titleRef = useRef<HTMLDivElement>(null);
    useEffect(
        () => {
            if (isCompleted) {
                setExpanded(false);
            }
        },
        [isCompleted]
    );
    useEffect(
        () => {
            const checkTruncation = () => {
                if (titleRef.current) {
                    setIsTextTruncated(titleRef.current.scrollWidth > titleRef.current.clientWidth);
                }
            };
            checkTruncation();
            window.addEventListener('resize', checkTruncation);
            return () => window.removeEventListener('resize', checkTruncation);
        },
        [step.title]
    );
    const hasContent = (step?.steps || step?.elements) as unknown as boolean;
    const handleToggle = () => hasContent && setExpanded(!expanded);
    const statusBadge = step.status === 'failed'
        ? <StatusBadge isOuterLayer={isOuterLayer}>失败</StatusBadge>
        : null;

    return (
        <StepContainer isOuterLayer={isOuterLayer}>
            <StepHeader isOuterLayer={isOuterLayer} onClick={handleToggle}>
                {renderExpandIcon(isOuterLayer, hasContent, expanded)}
                <StepTitleWrapper isOuterLayer={isOuterLayer}>
                    {renderStepIcon(step, isOuterLayer)}
                    <StepTitle
                        ref={titleRef}
                        hasTooltip={isTextTruncated}
                        title={isTextTruncated ? step.title : undefined}
                    >
                        {step.title}
                    </StepTitle>
                    {isOuterLayer && statusBadge}
                </StepTitleWrapper>
                <Space>
                    {!isOuterLayer && statusBadge}
                    {renderInnerExpandIcon(isOuterLayer, hasContent, expanded)}
                </Space>
            </StepHeader>
            {renderStepContent(step, expanded, hasContent)}
        </StepContainer>
    );
}
export const PlaygroundStageStepsContent = ({steps, isOuterLayer = true}: ItemsProps) => (
    <Container isOuterLayer={isOuterLayer}>
        {steps?.map(step => (
            <StepItem key={step.stepId} step={step} isOuterLayer={isOuterLayer} />
        ))}
    </Container>
);
